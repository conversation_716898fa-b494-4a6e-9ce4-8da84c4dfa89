/* Happi Reminder - 基于1.css的设计风格 */
:root {
  --color-primary: #fbbf24; /* bright yellow */
  --color-primary-700: #d97706; /* darker yellow for hovers */
  --color-black: #000000; /* pure black */
  --color-white: #ffffff; /* pure white */
  --color-gray: #f5f5f5; /* very light gray */
  --radius-lg: 20px;
  --radius-md: 12px;
  --radius-sm: 8px;
  --shadow-1: 0 4px 12px rgba(0,0,0,.1);
  --shadow-2: 0 8px 24px rgba(0,0,0,.15);
}

/* CSS Reset */
*, *::before, *::after { box-sizing: border-box; }
html, body { height: 100%; }
body {
  margin: 0;
  font-family: Inter, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, sans-serif;
  color: var(--color-black);
  background: var(--color-white);
  overflow-x: hidden;
}
img { max-width: 100%; display: block; }
a { color: inherit; text-decoration: none; }
button { font: inherit; cursor: pointer; }

/* 基础工具类 */
.container { width: min(1100px, 92vw); margin: 0 auto; }
.btn {
  display: inline-flex; align-items: center; justify-content: center;
  padding: 12px 20px; border-radius: 999px; border: 2px solid var(--color-black); cursor: pointer;
  background: var(--color-white); color: var(--color-black); transition: all .2s ease;
  font-weight: 600; gap: 8px;
}
.btn:hover { background: var(--color-black); color: var(--color-white); }
.btn-primary { background: var(--color-primary); color: var(--color-black); border-color: var(--color-black); }
.btn-primary:hover { background: var(--color-primary-700); }

/* 顶部公告栏 */
.topbar {
  background: var(--color-black); color: var(--color-white); font-size: 13px; padding: 8px 0;
}
.topbar .container { display: flex; justify-content: center; align-items: center; gap: 8px; }

/* 头部导航 */
header {
  position: sticky; top: 0; z-index: 10; background: var(--color-primary);
  border-bottom: 2px solid var(--color-black);
}
.nav { display: flex; align-items: center; justify-content: space-between; padding: 16px 0; }
.brand { display: flex; align-items: center; gap: 10px; font-weight: 800; letter-spacing: 1px; }
.brand-mark {
  width: 40px; height: 40px; border-radius: 10px; background: var(--color-black); color: var(--color-white);
  display: grid; place-items: center; font-size: 22px; transform: rotate(-6deg); font-weight: 900;
}

/* Hero 区域 */
.hero {
  position: relative;
  background: var(--color-primary);
  padding: 60px 20px;
  margin: 20px;
  border-radius: var(--radius-lg);
  border: 4px solid var(--color-black);
  box-shadow: 0 8px 32px rgba(0,0,0,.15);
  text-align: center;
}
.hero-inner {
  display: grid;
  grid-template-columns: 1.1fr .9fr;
  gap: 40px;
  align-items: center;
  max-width: 1100px;
  margin: 0 auto;
}
.hero h1 {
  font-size: clamp(32px, 4.5vw, 56px); line-height: .95; margin: 0 0 16px 0;
  font-weight: 900;
}
.hero p { font-size: 18px; }
.hero-card {
  background: var(--color-white); border: 2px solid var(--color-black); border-radius: var(--radius-lg);
  box-shadow: var(--shadow-1); padding: 18px; transform: rotate(-6deg);
}
.hero-blob {
  aspect-ratio: 1 / 1; border-radius: 32% 68% 65% 35% / 46% 35% 65% 54%; 
  background: var(--color-black); width: 100%;
}

/* 添加提醒表单 */
.add-reminder-section {
  padding: 40px 0;
}
.add-reminder-form {
  background: var(--color-white);
  border: 3px solid var(--color-black);
  border-radius: var(--radius-lg);
  padding: 40px;
  box-shadow: var(--shadow-2);
  margin-bottom: 40px;
}
.add-reminder-form h2 {
  font-size: 28px;
  font-weight: 800;
  margin-bottom: 24px;
  text-align: center;
}

/* 表单输入 */
.form-group {
  margin-bottom: 24px;
}
#taskInput {
  width: 100%;
  padding: 16px 20px;
  border: 3px solid var(--color-black);
  border-radius: var(--radius-md);
  font-size: 16px;
  font-weight: 600;
  background: var(--color-white);
  transition: all 0.2s ease;
}
#taskInput:focus {
  outline: none;
  background: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(251, 191, 36, 0.3);
}

/* 时间滑动选择器 */
.time-slider-container {
  background: var(--color-gray);
  border: 2px solid var(--color-black);
  border-radius: var(--radius-md);
  padding: 24px;
  margin-bottom: 24px;
}
.time-slider-container h3 {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 20px;
  text-align: center;
}
.time-sliders {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}
.slider-group {
  text-align: center;
}
.slider-group label {
  display: block;
  font-weight: 600;
  margin-bottom: 8px;
  font-size: 14px;
}
.slider {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: var(--color-white);
  border: 2px solid var(--color-black);
  outline: none;
  margin-bottom: 8px;
  cursor: pointer;
}
.slider::-webkit-slider-thumb {
  appearance: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--color-primary);
  border: 2px solid var(--color-black);
  cursor: pointer;
}
.slider::-moz-range-thumb {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--color-primary);
  border: 2px solid var(--color-black);
  cursor: pointer;
}
.slider-group span {
  display: inline-block;
  background: var(--color-black);
  color: var(--color-white);
  padding: 4px 12px;
  border-radius: var(--radius-sm);
  font-weight: 600;
  font-size: 14px;
}

/* 选中时间显示 */
.selected-time {
  text-align: center;
  background: var(--color-primary);
  border: 2px solid var(--color-black);
  border-radius: var(--radius-md);
  padding: 12px 20px;
  font-size: 18px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* 提醒列表 */
.reminders-section {
  padding: 20px 0;
}
.reminders-section h2 {
  font-size: 32px;
  font-weight: 800;
  text-align: center;
  margin-bottom: 32px;
}
.reminders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

/* 提醒卡片 */
.reminder-card {
  border: 3px solid var(--color-black);
  border-radius: var(--radius-lg);
  padding: 24px;
  box-shadow: 4px 6px 0 rgba(0,0,0,.2);
  transition: all 0.2s ease;
  position: relative;
  min-height: 180px;
  display: flex;
  flex-direction: column;
}
.reminder-card:hover {
  transform: translateY(-2px);
  box-shadow: 6px 8px 0 rgba(0,0,0,.2);
}

/* 卡片颜色 */
.reminder-card.color-1 { background: #ff6b6b; }
.reminder-card.color-2 { background: #4ecdc4; }
.reminder-card.color-3 { background: var(--color-primary); }
.reminder-card.color-4 { background: #a8e6cf; }
.reminder-card.color-5 { background: #ffd93d; }
.reminder-card.color-6 { background: #ff8cc8; }

.reminder-title {
  font-size: 18px;
  font-weight: 700;
  color: var(--color-black);
  margin-bottom: 12px;
  line-height: 1.3;
}
.reminder-time {
  background: var(--color-black);
  color: var(--color-white);
  padding: 6px 12px;
  border-radius: var(--radius-sm);
  font-size: 12px;
  font-weight: 600;
  display: inline-block;
  margin-bottom: 16px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
.reminder-time.expired {
  background: #ff4757;
  animation: pulse 2s infinite;
}
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}
.reminder-actions {
  margin-top: auto;
  display: flex;
  justify-content: flex-end;
}
.reminder-btn {
  background: var(--color-white);
  border: 2px solid var(--color-black);
  padding: 8px 16px;
  border-radius: var(--radius-sm);
  font-weight: 600;
  font-size: 12px;
  transition: all 0.2s ease;
}
.reminder-btn:hover {
  background: var(--color-black);
  color: var(--color-white);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: rgba(0,0,0,0.6);
}
.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
}
.empty-state h3 {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 8px;
}
.empty-state p {
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 900px) {
  .hero-inner {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  .time-sliders {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  .reminders-grid {
    grid-template-columns: 1fr;
  }
}
