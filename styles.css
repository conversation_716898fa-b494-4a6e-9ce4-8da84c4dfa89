* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #FFC107 0%, #FF9800 50%, #FF5722 100%);
    min-height: 100vh;
    color: #333;
    padding: 20px 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 40px;
}

.header h1 {
    font-size: 3rem;
    font-weight: 900;
    color: #333;
    margin-bottom: 30px;
    text-transform: uppercase;
    letter-spacing: 2px;
}

/* 添加任务表单 */
.add-task-form {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.form-row {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

#taskInput {
    flex: 2;
    min-width: 200px;
    padding: 15px 20px;
    border: 3px solid #e0e0e0;
    border-radius: 15px;
    font-size: 1.1rem;
    font-weight: 600;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

#taskInput:focus {
    outline: none;
    border-color: #FFC107;
    background: white;
    box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.2);
}

#timeInput {
    flex: 1;
    min-width: 180px;
    padding: 15px 20px;
    border: 3px solid #e0e0e0;
    border-radius: 15px;
    font-size: 1rem;
    font-weight: 600;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

#timeInput:focus {
    outline: none;
    border-color: #FFC107;
    background: white;
    box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.2);
}

#addBtn {
    background: #FFC107;
    color: #333;
    border: none;
    padding: 15px 30px;
    border-radius: 15px;
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
    white-space: nowrap;
}

#addBtn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4);
    background: #FFD54F;
}

/* 任务网格 */
.tasks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

/* 任务卡片 - 简化版 */
.task-card {
    border-radius: 15px;
    padding: 0;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    border: 3px solid rgba(255, 255, 255, 0.2);
}

.task-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
}

.task-card.completed {
    opacity: 0.5;
    filter: grayscale(0.5);
}

.task-card.completed .task-title {
    text-decoration: line-through;
}

.task-card.expired {
    border-color: #FF5722;
    box-shadow: 0 8px 25px rgba(255, 87, 34, 0.3);
}

/* 任务卡片颜色 */
.task-card.color-1 { background: #FFC107; }
.task-card.color-2 { background: #2196F3; }
.task-card.color-3 { background: #FF5722; }
.task-card.color-4 { background: #9C27B0; }
.task-card.color-5 { background: #4CAF50; }
.task-card.color-6 { background: #E91E63; }

.task-content {
    padding: 25px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.task-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: white;
    margin-bottom: 15px;
    line-height: 1.3;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.task-time {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 15px;
    display: inline-block;
}

.task-time.expired {
    background: #FF5722;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.task-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: auto;
}

.task-btn {
    background: #FFC107;
    border: none;
    padding: 10px 15px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #333;
    font-weight: 700;
    font-size: 0.9rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.task-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    background: #FFD54F;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 80px 20px;
    color: rgba(255, 255, 255, 0.8);
}

.empty-state i {
    font-size: 5rem;
    margin-bottom: 25px;
    opacity: 0.6;
}

.empty-state h3 {
    font-size: 2rem;
    margin-bottom: 15px;
    font-weight: 700;
}

.empty-state p {
    font-size: 1.1rem;
}



/* 模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 20px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

.modal-content.small {
    max-width: 400px;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.modal-header h2 {
    font-size: 1.5rem;
    color: #333;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(0, 0, 0, 0.1);
    transform: scale(1.1);
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #4ecdc4;
    background: white;
    box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.modal-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 25px;
}

.btn-cancel,
.btn-save,
.btn-delete {
    padding: 12px 24px;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-cancel {
    background: #e0e0e0;
    color: #666;
}

.btn-cancel:hover {
    background: #d0d0d0;
}

.btn-save {
    background: #FFC107;
    color: #333;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
    font-weight: 700;
}

.btn-save:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
    background: #FFD54F;
}

.btn-delete {
    background: #F44336;
    color: white;
    box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
    font-weight: 700;
}

.btn-delete:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
    background: #E57373;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .form-row {
        flex-direction: column;
        align-items: stretch;
    }

    #taskInput, #timeInput, #addBtn {
        min-width: auto;
        width: 100%;
    }

    .tasks-grid {
        grid-template-columns: 1fr;
    }

    .task-card {
        min-height: 160px;
    }

    .task-title {
        font-size: 1.1rem;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}
