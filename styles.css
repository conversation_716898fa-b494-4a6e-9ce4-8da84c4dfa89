* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', 'Microsoft YaHei', sans-serif;
    background: linear-gradient(135deg, #FFC107 0%, #FF9800 50%, #FF5722 100%);
    min-height: 100vh;
    color: #333;
    padding: 20px 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 40px;
}

.header h1 {
    font-size: 3rem;
    font-weight: 900;
    color: #333;
    margin-bottom: 30px;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.header-actions {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.btn-add {
    background: #FFC107;
    color: #333;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn-add:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    background: #FFD54F;
}

.filter-buttons {
    display: flex;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 25px;
    padding: 4px;
    gap: 4px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.filter-btn {
    background: transparent;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    color: #666;
}

.filter-btn.active {
    background: #333;
    color: white;
}

/* 任务网格 */
.tasks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

/* 任务卡片 - 模仿图片设计 */
.task-card {
    border-radius: 15px;
    padding: 0;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-height: 280px;
    display: flex;
    flex-direction: column;
    border: 3px solid rgba(255, 255, 255, 0.2);
}

.task-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.25);
}

.task-card.completed {
    opacity: 0.6;
    filter: grayscale(0.3);
}

.task-card.completed .task-title {
    text-decoration: line-through;
}

/* 任务卡片颜色 */
.task-card.color-1 { background: #FFC107; }
.task-card.color-2 { background: #2196F3; }
.task-card.color-3 { background: #FF5722; }
.task-card.color-4 { background: #9C27B0; }
.task-card.color-5 { background: #4CAF50; }
.task-card.color-6 { background: #E91E63; }

.task-content {
    padding: 25px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.task-header {
    margin-bottom: 15px;
}

.task-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: white;
    margin-bottom: 10px;
    line-height: 1.3;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.task-description {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.5;
    margin-bottom: 15px;
    font-size: 0.95rem;
}

.task-tag {
    display: inline-block;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.task-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
}

.task-date {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
    font-weight: 600;
}

.task-date.overdue {
    color: #FFEB3B;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.task-actions {
    display: flex;
    gap: 8px;
}

.task-btn {
    background: #FFC107;
    border: none;
    padding: 8px 12px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #333;
    font-weight: 700;
    font-size: 0.8rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.task-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    background: #FFD54F;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 80px 20px;
    color: rgba(255, 255, 255, 0.8);
}

.empty-state i {
    font-size: 5rem;
    margin-bottom: 25px;
    opacity: 0.6;
}

.empty-state h3 {
    font-size: 2rem;
    margin-bottom: 15px;
    font-weight: 700;
}

.empty-state p {
    font-size: 1.1rem;
}



/* 模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 20px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
}

.modal-content.small {
    max-width: 400px;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.modal-header h2 {
    font-size: 1.5rem;
    color: #333;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(0, 0, 0, 0.1);
    transform: scale(1.1);
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #4ecdc4;
    background: white;
    box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.modal-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 25px;
}

.btn-cancel,
.btn-save,
.btn-delete {
    padding: 12px 24px;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-cancel {
    background: #e0e0e0;
    color: #666;
}

.btn-cancel:hover {
    background: #d0d0d0;
}

.btn-save {
    background: #FFC107;
    color: #333;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
    font-weight: 700;
}

.btn-save:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
    background: #FFD54F;
}

.btn-delete {
    background: #F44336;
    color: white;
    box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
    font-weight: 700;
}

.btn-delete:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
    background: #E57373;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .header {
        flex-direction: column;
        text-align: center;
    }

    .header h1 {
        font-size: 2rem;
    }

    .tasks-grid {
        grid-template-columns: 1fr;
    }

    .stats {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .modal-actions {
        flex-direction: column;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}
