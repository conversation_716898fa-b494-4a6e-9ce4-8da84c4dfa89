const { app, BrowserWindow, Tray, Menu, nativeImage, ipcMain, Notification } = require('electron');
const path = require('path');

let mainWindow;
let tray;

// 创建主窗口
function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1000,
        height: 700,
        minWidth: 800,
        minHeight: 600,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false
        },
        icon: path.join(__dirname, 'assets', 'icon.png'),
        show: false,
        titleBarStyle: 'default'
    });

    mainWindow.loadFile('index.html');

    // 窗口准备好后显示
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
    });

    // 关闭窗口时隐藏到托盘而不是退出
    mainWindow.on('close', (event) => {
        if (!app.isQuiting) {
            event.preventDefault();
            mainWindow.hide();
        }
        return false;
    });
}

// 创建系统托盘
function createTray() {
    // 创建托盘图标
    const iconPath = path.join(__dirname, 'assets', 'tray-icon.png');
    const trayIcon = nativeImage.createFromPath(iconPath);
    trayIcon.setTemplateImage(true);
    
    tray = new Tray(trayIcon);
    
    // 设置托盘提示
    tray.setToolTip('Happi Reminder - 待办提醒');
    
    // 创建托盘菜单
    const contextMenu = Menu.buildFromTemplate([
        {
            label: '显示窗口',
            click: () => {
                mainWindow.show();
                mainWindow.focus();
            }
        },
        {
            label: '添加提醒',
            click: () => {
                mainWindow.show();
                mainWindow.focus();
                mainWindow.webContents.send('focus-input');
            }
        },
        { type: 'separator' },
        {
            label: '退出',
            click: () => {
                app.isQuiting = true;
                app.quit();
            }
        }
    ]);
    
    tray.setContextMenu(contextMenu);
    
    // 双击托盘图标显示窗口
    tray.on('double-click', () => {
        mainWindow.show();
        mainWindow.focus();
    });
}

// 应用准备就绪
app.whenReady().then(() => {
    createWindow();
    createTray();
    
    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            createWindow();
        }
    });
});

// 所有窗口关闭时的处理
app.on('window-all-closed', () => {
    // 在 macOS 上，保持应用运行
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

// IPC 通信处理
ipcMain.handle('show-notification', async (event, { title, body }) => {
    if (Notification.isSupported()) {
        const notification = new Notification({
            title: title,
            body: body,
            icon: path.join(__dirname, 'assets', 'icon.png'),
            sound: true
        });
        
        notification.show();
        
        notification.on('click', () => {
            mainWindow.show();
            mainWindow.focus();
        });
        
        return true;
    }
    return false;
});

// 获取应用路径
ipcMain.handle('get-app-path', () => {
    return app.getPath('userData');
});

// 最小化到托盘
ipcMain.handle('minimize-to-tray', () => {
    mainWindow.hide();
});

// 设置托盘闪烁（提醒时）
ipcMain.handle('flash-tray', () => {
    if (tray) {
        // 简单的闪烁效果
        let flashCount = 0;
        const flashInterval = setInterval(() => {
            if (flashCount >= 6) {
                clearInterval(flashInterval);
                return;
            }
            
            const iconPath = flashCount % 2 === 0 
                ? path.join(__dirname, 'assets', 'tray-icon-alert.png')
                : path.join(__dirname, 'assets', 'tray-icon.png');
            
            const icon = nativeImage.createFromPath(iconPath);
            icon.setTemplateImage(true);
            tray.setImage(icon);
            
            flashCount++;
        }, 500);
    }
});
