// 创建简单的图标占位符
const fs = require('fs');
const path = require('path');

// 创建SVG图标
const createSVGIcon = (size, content, filename) => {
    const svg = `
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
    <rect width="${size}" height="${size}" fill="#fbbf24" stroke="#000" stroke-width="4" rx="8"/>
    <text x="50%" y="50%" text-anchor="middle" dy="0.35em" font-family="Arial, sans-serif" font-size="${size * 0.4}" font-weight="bold" fill="#000">H</text>
    ${content}
</svg>`;
    
    fs.writeFileSync(path.join(__dirname, 'assets', filename), svg.trim());
    console.log(`Created ${filename}`);
};

// 创建主图标
createSVGIcon(256, '', 'icon.svg');

// 创建托盘图标
createSVGIcon(32, '', 'tray-icon.svg');

// 创建提醒托盘图标
createSVGIcon(32, '<circle cx="24" cy="8" r="4" fill="#ff4757"/>', 'tray-icon-alert.svg');

console.log('图标创建完成！');
console.log('注意：这些是SVG占位符图标。');
console.log('在生产环境中，请替换为高质量的PNG/ICO/ICNS图标文件。');
