@echo off
echo ========================================
echo    Happi Reminder 桌面应用启动器
echo ========================================
echo.

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未找到Node.js
    echo 请先安装Node.js: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js已安装
echo.

REM 检查是否已安装依赖
if not exist "node_modules" (
    echo 📦 正在安装依赖...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
    echo.
)

echo 🚀 启动Happi Reminder...
echo.
echo 💡 提示：
echo   - 关闭窗口后应用会最小化到系统托盘
echo   - 双击托盘图标可重新打开窗口
echo   - 右键托盘图标查看更多选项
echo.

npm start
