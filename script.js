// 简单的提醒管理系统
class ReminderManager {
    constructor() {
        this.reminders = JSON.parse(localStorage.getItem('reminders')) || [];
        this.checkInterval = null;
        this.init();
    }

    init() {
        this.requestNotificationPermission();
        this.renderReminders();
        this.startChecking();
        this.setDefaultTime();
    }

    // 请求通知权限
    async requestNotificationPermission() {
        if ('Notification' in window) {
            if (Notification.permission === 'default') {
                await Notification.requestPermission();
            }
        }
    }

    // 设置默认时间为当前时间+1小时
    setDefaultTime() {
        const now = new Date();
        now.setHours(now.getHours() + 1);
        now.setMinutes(0);
        const timeString = now.toISOString().slice(0, 16);
        document.getElementById('timeInput').value = timeString;
    }

    // 生成唯一ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // 添加提醒
    addReminder(text, time) {
        if (!text.trim()) {
            alert('请输入提醒内容');
            return;
        }

        if (!time) {
            alert('请选择提醒时间');
            return;
        }

        const reminderTime = new Date(time);
        const now = new Date();

        if (reminderTime <= now) {
            alert('提醒时间必须是未来时间');
            return;
        }

        const reminder = {
            id: this.generateId(),
            text: text.trim(),
            time: time,
            completed: false,
            notified: false,
            createdAt: new Date().toISOString()
        };

        this.reminders.unshift(reminder);
        this.saveReminders();
        this.renderReminders();

        // 清空输入框
        document.getElementById('taskInput').value = '';
        this.setDefaultTime();

        alert('提醒添加成功！');
    }

    // 删除提醒
    deleteReminder(id) {
        this.reminders = this.reminders.filter(reminder => reminder.id !== id);
        this.saveReminders();
        this.renderReminders();
    }

    // 标记完成
    completeReminder(id) {
        const reminder = this.reminders.find(r => r.id === id);
        if (reminder) {
            reminder.completed = true;
            this.saveReminders();
            this.renderReminders();
        }
    }

    // 保存到本地存储
    saveReminders() {
        localStorage.setItem('reminders', JSON.stringify(this.reminders));
    }

    // 渲染提醒列表
    renderReminders() {
        const tasksGrid = document.getElementById('tasksGrid');
        const emptyState = document.getElementById('emptyState');
        const activeReminders = this.reminders.filter(r => !r.completed);

        if (activeReminders.length === 0) {
            tasksGrid.style.display = 'none';
            emptyState.style.display = 'block';
            return;
        }

        tasksGrid.style.display = 'grid';
        emptyState.style.display = 'none';

        tasksGrid.innerHTML = activeReminders.map((reminder, index) => this.createReminderCard(reminder, index)).join('');
    }

    // 创建提醒卡片
    createReminderCard(reminder, index) {
        const now = new Date();
        const reminderTime = new Date(reminder.time);
        const isExpired = reminderTime <= now;
        const colorClass = `color-${(index % 6) + 1}`;

        return `
            <div class="task-card ${colorClass} ${reminder.completed ? 'completed' : ''} ${isExpired ? 'expired' : ''}" data-id="${reminder.id}">
                <div class="task-content">
                    <div class="task-title">${this.escapeHtml(reminder.text)}</div>
                    <div class="task-time ${isExpired ? 'expired' : ''}">${this.formatTime(reminder.time)}</div>
                    <div class="task-footer">
                        <button class="task-btn" onclick="reminderManager.deleteReminder('${reminder.id}')" title="删除提醒">
                            删除
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // 格式化时间显示
    formatTime(timeString) {
        const date = new Date(timeString);
        const now = new Date();
        const diffTime = date - now;
        const diffMinutes = Math.floor(diffTime / (1000 * 60));
        const diffHours = Math.floor(diffMinutes / 60);
        const diffDays = Math.floor(diffHours / 24);

        if (diffTime <= 0) {
            return '已过期';
        } else if (diffMinutes < 60) {
            return `${diffMinutes}分钟后`;
        } else if (diffHours < 24) {
            return `${diffHours}小时后`;
        } else if (diffDays === 1) {
            return '明天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        } else {
            return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        }
    }

    // 开始检查提醒
    startChecking() {
        // 每30秒检查一次
        this.checkInterval = setInterval(() => {
            this.checkReminders();
        }, 30000);

        // 立即检查一次
        this.checkReminders();
    }

    // 检查提醒
    checkReminders() {
        const now = new Date();

        this.reminders.forEach(reminder => {
            if (!reminder.notified && !reminder.completed) {
                const reminderTime = new Date(reminder.time);

                // 如果到了提醒时间（允许30秒误差）
                if (now >= reminderTime && (now - reminderTime) <= 30000) {
                    this.showNotification(reminder);
                    reminder.notified = true;
                    this.saveReminders();
                }
            }
        });

        // 重新渲染以更新过期状态
        this.renderReminders();
    }

    // 显示通知
    showNotification(reminder) {
        if ('Notification' in window && Notification.permission === 'granted') {
            const notification = new Notification('提醒时间到了！', {
                body: reminder.text,
                icon: 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23FFC107"><path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.9 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/></svg>',
                tag: reminder.id,
                requireInteraction: true
            });

            notification.onclick = () => {
                window.focus();
                notification.close();
            };

            // 5秒后自动关闭
            setTimeout(() => {
                notification.close();
            }, 5000);
        } else {
            // 如果不支持通知，使用alert
            alert(`提醒时间到了！\n${reminder.text}`);
        }
    }

    // HTML转义
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// 全局函数
function addTask() {
    const taskInput = document.getElementById('taskInput');
    const timeInput = document.getElementById('timeInput');

    reminderManager.addReminder(taskInput.value, timeInput.value);
}

// 键盘事件
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('taskInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            addTask();
        }
    });
});

// 初始化应用
const reminderManager = new ReminderManager();
