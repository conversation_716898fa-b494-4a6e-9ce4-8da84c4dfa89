// 任务管理类
class TaskManager {
    constructor() {
        this.tasks = JSON.parse(localStorage.getItem('tasks')) || [];
        this.currentFilter = 'all';
        this.editingTaskId = null;
        this.taskToDelete = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.renderTasks();
        this.updateStats();
    }

    bindEvents() {
        // 表单提交
        document.getElementById('taskForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveTask();
        });

        // 过滤按钮
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.setFilter(e.target.dataset.filter);
            });
        });

        // 模态框外部点击关闭
        document.getElementById('taskModal').addEventListener('click', (e) => {
            if (e.target.id === 'taskModal') {
                this.closeModal();
            }
        });

        document.getElementById('deleteModal').addEventListener('click', (e) => {
            if (e.target.id === 'deleteModal') {
                this.closeDeleteModal();
            }
        });
    }

    // 生成唯一ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // 添加任务
    addTask(taskData) {
        const task = {
            id: this.generateId(),
            title: taskData.title,
            description: taskData.description,
            priority: taskData.priority,
            category: taskData.category,
            dueDate: taskData.dueDate,
            completed: false,
            createdAt: new Date().toISOString(),
            completedAt: null
        };

        this.tasks.unshift(task);
        this.saveTasks();
        this.renderTasks();
        this.updateStats();
    }

    // 更新任务
    updateTask(id, taskData) {
        const taskIndex = this.tasks.findIndex(task => task.id === id);
        if (taskIndex !== -1) {
            this.tasks[taskIndex] = {
                ...this.tasks[taskIndex],
                title: taskData.title,
                description: taskData.description,
                priority: taskData.priority,
                category: taskData.category,
                dueDate: taskData.dueDate
            };
            this.saveTasks();
            this.renderTasks();
            this.updateStats();
        }
    }

    // 切换任务完成状态
    toggleTask(id) {
        const task = this.tasks.find(task => task.id === id);
        if (task) {
            task.completed = !task.completed;
            task.completedAt = task.completed ? new Date().toISOString() : null;
            this.saveTasks();
            this.renderTasks();
            this.updateStats();
        }
    }

    // 删除任务
    deleteTask(id) {
        this.tasks = this.tasks.filter(task => task.id !== id);
        this.saveTasks();
        this.renderTasks();
        this.updateStats();
    }

    // 保存到本地存储
    saveTasks() {
        localStorage.setItem('tasks', JSON.stringify(this.tasks));
    }

    // 设置过滤器
    setFilter(filter) {
        this.currentFilter = filter;
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-filter="${filter}"]`).classList.add('active');
        this.renderTasks();
    }

    // 获取过滤后的任务
    getFilteredTasks() {
        switch (this.currentFilter) {
            case 'pending':
                return this.tasks.filter(task => !task.completed);
            case 'completed':
                return this.tasks.filter(task => task.completed);
            default:
                return this.tasks;
        }
    }

    // 渲染任务列表
    renderTasks() {
        const tasksGrid = document.getElementById('tasksGrid');
        const emptyState = document.getElementById('emptyState');
        const filteredTasks = this.getFilteredTasks();

        if (filteredTasks.length === 0) {
            tasksGrid.style.display = 'none';
            emptyState.style.display = 'block';
            return;
        }

        tasksGrid.style.display = 'grid';
        emptyState.style.display = 'none';

        tasksGrid.innerHTML = filteredTasks.map((task, index) => this.createTaskCard(task, index)).join('');
    }

    // 创建任务卡片 - 模仿图片设计
    createTaskCard(task, index) {
        const isOverdue = task.dueDate && new Date(task.dueDate) < new Date() && !task.completed;
        const dueDateText = task.dueDate ? this.formatDate(task.dueDate) : this.formatDate(task.createdAt);
        const colorClass = `color-${(index % 6) + 1}`;

        return `
            <div class="task-card ${colorClass} ${task.completed ? 'completed' : ''}" data-id="${task.id}">
                <div class="task-content">
                    <div class="task-header">
                        <div class="task-title">${this.escapeHtml(task.title)}</div>
                        ${task.description ? `<div class="task-description">${this.escapeHtml(task.description)}</div>` : ''}
                        <div class="task-tag">${this.getPriorityName(task.priority)}</div>
                    </div>
                    <div class="task-footer">
                        <div class="task-date ${isOverdue ? 'overdue' : ''}">
                            ${dueDateText}
                        </div>
                        <div class="task-actions">
                            <button class="task-btn" onclick="taskManager.toggleTask('${task.id}')" title="${task.completed ? '撤销' : '完成'}">
                                ${task.completed ? '撤销' : '完成'}
                            </button>
                            <button class="task-btn" onclick="taskManager.editTask('${task.id}')" title="编辑">
                                编辑
                            </button>
                            <button class="task-btn" onclick="taskManager.showDeleteModal('${task.id}')" title="删除">
                                删除
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // 获取分类图标
    getCategoryIcon(category) {
        const icons = {
            work: 'fa-briefcase',
            personal: 'fa-user',
            study: 'fa-book',
            health: 'fa-heart',
            other: 'fa-tag'
        };
        return icons[category] || 'fa-tag';
    }

    // 获取分类名称
    getCategoryName(category) {
        const names = {
            work: '工作',
            personal: '个人',
            study: '学习',
            health: '健康',
            other: '其他'
        };
        return names[category] || '其他';
    }

    // 获取优先级名称
    getPriorityName(priority) {
        const names = {
            high: '高优先级',
            medium: '中优先级',
            low: '低优先级'
        };
        return names[priority] || '中优先级';
    }

    // 格式化日期
    formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = date - now;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 0) {
            return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        } else if (diffDays === 1) {
            return '明天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        } else if (diffDays === -1) {
            return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        } else if (diffDays > 1 && diffDays <= 7) {
            return `${diffDays}天后 ` + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        } else if (diffDays < -1 && diffDays >= -7) {
            return `${Math.abs(diffDays)}天前 ` + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        } else {
            return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        }
    }

    // HTML转义
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 更新统计信息（简化版，因为移除了统计卡片）
    updateStats() {
        // 可以在这里添加其他统计逻辑，比如更新页面标题等
        const total = this.tasks.length;
        const completed = this.tasks.filter(task => task.completed).length;
        const pending = total - completed;

        // 更新页面标题显示任务数量
        document.title = `TODO • 待办事项管理 (${pending}/${total})`;
    }

    // 显示添加任务模态框
    showAddModal() {
        this.editingTaskId = null;
        document.getElementById('modalTitle').textContent = '添加任务';
        document.getElementById('taskForm').reset();
        document.getElementById('taskModal').classList.add('show');
    }

    // 编辑任务
    editTask(id) {
        const task = this.tasks.find(task => task.id === id);
        if (task) {
            this.editingTaskId = id;
            document.getElementById('modalTitle').textContent = '编辑任务';
            document.getElementById('taskTitle').value = task.title;
            document.getElementById('taskDescription').value = task.description || '';
            document.getElementById('taskPriority').value = task.priority;
            document.getElementById('taskCategory').value = task.category;
            document.getElementById('taskDueDate').value = task.dueDate || '';
            document.getElementById('taskModal').classList.add('show');
        }
    }

    // 保存任务
    saveTask() {
        const formData = {
            title: document.getElementById('taskTitle').value.trim(),
            description: document.getElementById('taskDescription').value.trim(),
            priority: document.getElementById('taskPriority').value,
            category: document.getElementById('taskCategory').value,
            dueDate: document.getElementById('taskDueDate').value
        };

        if (!formData.title) {
            alert('请输入任务标题');
            return;
        }

        if (this.editingTaskId) {
            this.updateTask(this.editingTaskId, formData);
        } else {
            this.addTask(formData);
        }

        this.closeModal();
    }

    // 关闭模态框
    closeModal() {
        document.getElementById('taskModal').classList.remove('show');
        this.editingTaskId = null;
    }

    // 显示删除确认模态框
    showDeleteModal(id) {
        this.taskToDelete = id;
        document.getElementById('deleteModal').classList.add('show');
    }

    // 关闭删除确认模态框
    closeDeleteModal() {
        document.getElementById('deleteModal').classList.remove('show');
        this.taskToDelete = null;
    }

    // 确认删除
    confirmDelete() {
        if (this.taskToDelete) {
            this.deleteTask(this.taskToDelete);
            this.closeDeleteModal();
        }
    }
}

// 全局函数
function showAddModal() {
    taskManager.showAddModal();
}

function closeModal() {
    taskManager.closeModal();
}

function closeDeleteModal() {
    taskManager.closeDeleteModal();
}

function confirmDelete() {
    taskManager.confirmDelete();
}

// 初始化应用
const taskManager = new TaskManager();
