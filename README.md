# 桌面待办事项管理应用

一个现代化的桌面待办事项管理应用，采用卡片式设计，功能齐全，界面美观。

## 🌟 功能特性

### 核心功能
- ✅ **任务管理**：添加、编辑、删除、完成任务
- 🏷️ **分类管理**：工作、个人、学习、健康、其他
- ⭐ **优先级设置**：高、中、低三个优先级
- 📅 **截止日期**：设置任务截止时间，自动提醒过期
- 📊 **统计信息**：实时显示总任务、待完成、已完成数量
- 🔍 **任务过滤**：按状态筛选任务（全部、待完成、已完成）

### 界面特色
- 🎨 **现代设计**：模仿参考图片的卡片式设计风格
- 🌈 **丰富色彩**：每个优先级和分类都有独特的颜色标识
- 📱 **响应式布局**：适配不同屏幕尺寸
- ✨ **流畅动画**：悬停效果和过渡动画
- 🔄 **实时更新**：数据实时保存到本地存储

### 数据管理
- 💾 **本地存储**：使用 localStorage 保存数据
- 🔄 **自动保存**：所有操作自动保存，无需手动保存
- 📤 **数据持久化**：刷新页面后数据不丢失

## 🚀 快速开始

1. **下载文件**：确保以下文件在同一目录下
   - `index.html`
   - `styles.css`
   - `script.js`

2. **打开应用**：双击 `index.html` 文件在浏览器中打开

3. **开始使用**：点击"添加任务"按钮创建你的第一个待办事项

## 📖 使用指南

### 添加任务
1. 点击右上角的"添加任务"按钮
2. 填写任务信息：
   - **任务标题**（必填）
   - **任务描述**（可选）
   - **优先级**：高/中/低
   - **分类**：工作/个人/学习/健康/其他
   - **截止日期**（可选）
3. 点击"保存"按钮

### 管理任务
- **完成任务**：点击任务卡片右上角的 ✓ 按钮
- **编辑任务**：点击编辑按钮（铅笔图标）
- **删除任务**：点击删除按钮（垃圾桶图标）
- **撤销完成**：对已完成的任务点击撤销按钮

### 筛选任务
使用顶部的筛选按钮：
- **全部**：显示所有任务
- **待完成**：只显示未完成的任务
- **已完成**：只显示已完成的任务

## 🎨 设计特色

### 颜色系统
- **优先级颜色**：
  - 高优先级：红色渐变 (#ff6b6b)
  - 中优先级：橙色渐变 (#ffa726)
  - 低优先级：青色渐变 (#4ecdc4)

- **分类颜色**：
  - 工作：紫蓝渐变
  - 个人：红色渐变
  - 学习：青色渐变
  - 健康：绿色渐变
  - 其他：橙色渐变

### 视觉效果
- 毛玻璃效果背景
- 卡片悬停动画
- 渐变色彩搭配
- 圆角设计
- 阴影效果

## 🔧 技术栈

- **HTML5**：页面结构
- **CSS3**：样式和动画
- **JavaScript (ES6+)**：交互逻辑
- **Font Awesome**：图标库
- **LocalStorage**：数据存储

## 📱 响应式支持

应用完全支持响应式设计，在以下设备上都能良好运行：
- 桌面电脑
- 平板电脑
- 手机

## 🔒 数据安全

- 所有数据存储在本地浏览器中
- 不会上传到任何服务器
- 数据完全由用户控制

## 🎯 使用场景

- 个人日常任务管理
- 工作项目跟踪
- 学习计划安排
- 生活事务提醒
- 团队任务分配（本地使用）

## 📝 更新日志

### v1.0.0
- 基础任务管理功能
- 卡片式界面设计
- 分类和优先级系统
- 本地数据存储
- 响应式布局

## 🤝 贡献

欢迎提出建议和改进意见！

## 📄 许可证

MIT License - 可自由使用和修改
