{"name": "happi-reminder", "version": "1.0.0", "description": "A colorful desktop reminder app with system tray", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder", "dist": "electron-builder --publish=never"}, "keywords": ["reminder", "desktop", "electron", "tray"], "author": "Your Name", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "build": {"appId": "com.happi.reminder", "productName": "<PERSON><PERSON><PERSON>", "directories": {"output": "dist"}, "files": ["main.js", "renderer.js", "index.html", "styles.css", "assets/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}