const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// 提醒管理器类
class HappiReminderManager {
    constructor() {
        this.reminders = JSON.parse(localStorage.getItem('happi-reminders')) || [];
        this.checkInterval = null;
        this.init();
    }

    init() {
        this.setupSliders();
        this.renderReminders();
        this.startChecking();
        this.bindEvents();
    }

    // 设置滑动条
    setupSliders() {
        const daySlider = document.getElementById('daySlider');
        const hourSlider = document.getElementById('hourSlider');
        const minuteSlider = document.getElementById('minuteSlider');
        
        const dayValue = document.getElementById('dayValue');
        const hourValue = document.getElementById('hourValue');
        const minuteValue = document.getElementById('minuteValue');
        const selectedTime = document.getElementById('selectedTime');

        // 设置默认值
        const now = new Date();
        hourSlider.value = now.getHours();
        minuteSlider.value = Math.ceil(now.getMinutes() / 5) * 5;

        const updateTime = () => {
            const days = parseInt(daySlider.value);
            const hours = parseInt(hourSlider.value);
            const minutes = parseInt(minuteSlider.value);

            // 更新显示值
            dayValue.textContent = days === 0 ? '今天' : `${days}天后`;
            hourValue.textContent = hours.toString().padStart(2, '0');
            minuteValue.textContent = minutes.toString().padStart(2, '0');

            // 计算完整时间
            const targetDate = new Date();
            targetDate.setDate(targetDate.getDate() + days);
            targetDate.setHours(hours, minutes, 0, 0);

            // 更新选中时间显示
            const timeStr = this.formatSelectedTime(targetDate);
            selectedTime.textContent = timeStr;
        };

        // 绑定滑动条事件
        daySlider.addEventListener('input', updateTime);
        hourSlider.addEventListener('input', updateTime);
        minuteSlider.addEventListener('input', updateTime);

        // 初始化显示
        updateTime();
    }

    // 格式化选中时间
    formatSelectedTime(date) {
        const now = new Date();
        const diffDays = Math.floor((date - now) / (1000 * 60 * 60 * 24));
        
        if (diffDays === 0) {
            return `今天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
        } else if (diffDays === 1) {
            return `明天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
        } else {
            return `${diffDays}天后 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
        }
    }

    // 绑定事件
    bindEvents() {
        // 监听来自主进程的消息
        ipcRenderer.on('focus-input', () => {
            document.getElementById('taskInput').focus();
        });

        // 回车键添加提醒
        document.getElementById('taskInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.addReminder();
            }
        });
    }

    // 添加提醒
    async addReminder() {
        const taskInput = document.getElementById('taskInput');
        const text = taskInput.value.trim();

        if (!text) {
            alert('请输入提醒内容');
            return;
        }

        // 获取滑动条值
        const days = parseInt(document.getElementById('daySlider').value);
        const hours = parseInt(document.getElementById('hourSlider').value);
        const minutes = parseInt(document.getElementById('minuteSlider').value);

        // 计算提醒时间
        const reminderTime = new Date();
        reminderTime.setDate(reminderTime.getDate() + days);
        reminderTime.setHours(hours, minutes, 0, 0);

        // 检查时间是否有效
        if (reminderTime <= new Date()) {
            alert('提醒时间必须是未来时间');
            return;
        }

        // 创建提醒对象
        const reminder = {
            id: this.generateId(),
            text: text,
            time: reminderTime.toISOString(),
            completed: false,
            notified: false,
            createdAt: new Date().toISOString()
        };

        // 添加到列表
        this.reminders.unshift(reminder);
        this.saveReminders();
        this.renderReminders();

        // 清空输入框
        taskInput.value = '';
        taskInput.focus();

        // 显示成功消息
        this.showSuccessMessage('提醒添加成功！');
    }

    // 生成唯一ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // 删除提醒
    deleteReminder(id) {
        this.reminders = this.reminders.filter(r => r.id !== id);
        this.saveReminders();
        this.renderReminders();
    }

    // 保存提醒
    saveReminders() {
        localStorage.setItem('happi-reminders', JSON.stringify(this.reminders));
    }

    // 渲染提醒列表
    renderReminders() {
        const grid = document.getElementById('remindersGrid');
        const emptyState = document.getElementById('emptyState');
        const activeReminders = this.reminders.filter(r => !r.completed);

        if (activeReminders.length === 0) {
            grid.style.display = 'none';
            emptyState.style.display = 'block';
            return;
        }

        grid.style.display = 'grid';
        emptyState.style.display = 'none';

        grid.innerHTML = activeReminders.map((reminder, index) => 
            this.createReminderCard(reminder, index)
        ).join('');
    }

    // 创建提醒卡片
    createReminderCard(reminder, index) {
        const now = new Date();
        const reminderTime = new Date(reminder.time);
        const isExpired = reminderTime <= now;
        const colorClass = `color-${(index % 6) + 1}`;
        
        return `
            <div class="reminder-card ${colorClass}" data-id="${reminder.id}">
                <div class="reminder-title">${this.escapeHtml(reminder.text)}</div>
                <div class="reminder-time ${isExpired ? 'expired' : ''}">${this.formatTime(reminder.time)}</div>
                <div class="reminder-actions">
                    <button class="reminder-btn" onclick="reminderManager.deleteReminder('${reminder.id}')">
                        删除
                    </button>
                </div>
            </div>
        `;
    }

    // 格式化时间显示
    formatTime(timeString) {
        const date = new Date(timeString);
        const now = new Date();
        const diffTime = date - now;
        const diffMinutes = Math.floor(diffTime / (1000 * 60));
        const diffHours = Math.floor(diffMinutes / 60);
        const diffDays = Math.floor(diffHours / 24);

        if (diffTime <= 0) {
            return '已过期';
        } else if (diffMinutes < 60) {
            return `${diffMinutes}分钟后`;
        } else if (diffHours < 24) {
            return `${diffHours}小时后`;
        } else if (diffDays === 1) {
            return '明天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        } else {
            return `${diffDays}天后 ` + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        }
    }

    // 开始检查提醒
    startChecking() {
        // 每30秒检查一次
        this.checkInterval = setInterval(() => {
            this.checkReminders();
        }, 30000);
        
        // 立即检查一次
        this.checkReminders();
    }

    // 检查提醒
    async checkReminders() {
        const now = new Date();
        
        for (const reminder of this.reminders) {
            if (!reminder.notified && !reminder.completed) {
                const reminderTime = new Date(reminder.time);
                
                // 如果到了提醒时间（允许30秒误差）
                if (now >= reminderTime && (now - reminderTime) <= 30000) {
                    await this.showNotification(reminder);
                    reminder.notified = true;
                    this.saveReminders();
                    
                    // 闪烁托盘图标
                    ipcRenderer.invoke('flash-tray');
                }
            }
        }
        
        // 重新渲染以更新过期状态
        this.renderReminders();
    }

    // 显示通知
    async showNotification(reminder) {
        const success = await ipcRenderer.invoke('show-notification', {
            title: '⏰ 提醒时间到了！',
            body: reminder.text
        });
        
        if (!success) {
            // 如果系统通知失败，使用弹窗
            alert(`⏰ 提醒时间到了！\n${reminder.text}`);
        }
    }

    // HTML转义
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 显示成功消息
    showSuccessMessage(message) {
        // 创建临时提示
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--color-primary);
            color: var(--color-black);
            padding: 12px 20px;
            border-radius: var(--radius-md);
            border: 2px solid var(--color-black);
            font-weight: 600;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        `;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
}

// 全局函数
function addReminder() {
    reminderManager.addReminder();
}

function focusInput() {
    document.getElementById('taskInput').focus();
}

// 初始化应用
const reminderManager = new HappiReminderManager();

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);
