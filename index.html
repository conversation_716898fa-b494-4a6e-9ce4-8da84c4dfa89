<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Happi Reminder • 快乐提醒</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- 顶部公告栏 -->
    <div class="topbar">
        <div class="container">
            <span>🎉 Happi Reminder - 让提醒变得快乐简单！</span>
        </div>
    </div>

    <!-- 头部导航 -->
    <header>
        <div class="container">
            <nav class="nav">
                <div class="brand">
                    <div class="brand-mark">H</div>
                    <span>HAPPI REMINDER</span>
                </div>
                <div class="nav-actions">
                    <button class="btn btn-primary" onclick="focusInput()">
                        <i class="fas fa-plus"></i> 添加提醒
                    </button>
                </div>
            </nav>
        </div>
    </header>

    <div class="container">
        <!-- Hero 区域 -->
        <section class="hero">
            <div class="hero-inner">
                <div>
                    <h1>快乐提醒，轻松管理</h1>
                    <p>用最简单的方式管理你的待办事项，让每一个提醒都充满色彩！</p>
                </div>
                <div class="hero-card">
                    <div class="hero-blob"></div>
                </div>
            </div>
        </section>

        <!-- 添加提醒表单 -->
        <section class="add-reminder-section">
            <div class="add-reminder-form">
                <h2>添加新提醒</h2>
                <div class="form-group">
                    <input type="text" id="taskInput" placeholder="输入你的待办事项..." maxlength="100">
                </div>

                <!-- 时间滑动选择器 -->
                <div class="time-slider-container">
                    <h3>选择提醒时间</h3>
                    <div class="time-sliders">
                        <div class="slider-group">
                            <label>天数 (今天起)</label>
                            <input type="range" id="daySlider" min="0" max="10" value="0" class="slider">
                            <span id="dayValue">今天</span>
                        </div>
                        <div class="slider-group">
                            <label>小时</label>
                            <input type="range" id="hourSlider" min="0" max="23" value="12" class="slider">
                            <span id="hourValue">12</span>
                        </div>
                        <div class="slider-group">
                            <label>分钟</label>
                            <input type="range" id="minuteSlider" min="0" max="59" value="0" step="5" class="slider">
                            <span id="minuteValue">00</span>
                        </div>
                    </div>
                    <div class="selected-time">
                        <i class="fas fa-clock"></i>
                        <span id="selectedTime">今天 12:00</span>
                    </div>
                </div>

                <button id="addBtn" class="btn btn-primary" onclick="addReminder()">
                    <i class="fas fa-bell"></i> 添加提醒
                </button>
            </div>
        </section>

        <!-- 提醒列表 -->
        <section class="reminders-section">
            <h2>我的提醒</h2>
            <div class="reminders-grid" id="remindersGrid">
                <!-- 提醒卡片将在这里动态生成 -->
            </div>

            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <div class="empty-icon">🎯</div>
                <h3>还没有提醒哦</h3>
                <p>添加你的第一个提醒，让生活更有条理！</p>
            </div>
        </section>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
