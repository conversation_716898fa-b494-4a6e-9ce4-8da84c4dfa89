<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TODO • 待办事项管理</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <h1><i class="fas fa-tasks"></i> TODO • 待办事项管理</h1>
            <div class="header-actions">
                <button class="btn-add" onclick="showAddModal()">
                    <i class="fas fa-plus"></i> 添加任务
                </button>
                <div class="filter-buttons">
                    <button class="filter-btn active" data-filter="all">全部</button>
                    <button class="filter-btn" data-filter="pending">待完成</button>
                    <button class="filter-btn" data-filter="completed">已完成</button>
                </div>
            </div>
        </header>

        <!-- 统计信息 -->
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalTasks">0</div>
                <div class="stat-label">总任务</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pendingTasks">0</div>
                <div class="stat-label">待完成</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="completedTasks">0</div>
                <div class="stat-label">已完成</div>
            </div>
        </div>

        <!-- 任务列表 -->
        <div class="tasks-grid" id="tasksGrid">
            <!-- 任务卡片将在这里动态生成 -->
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="fas fa-clipboard-list"></i>
            <h3>暂无任务</h3>
            <p>点击"添加任务"创建你的第一个待办事项</p>
        </div>
    </div>

    <!-- 添加/编辑任务模态框 -->
    <div class="modal" id="taskModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">添加任务</h2>
                <button class="close-btn" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="taskForm">
                <div class="form-group">
                    <label for="taskTitle">任务标题</label>
                    <input type="text" id="taskTitle" required placeholder="输入任务标题...">
                </div>
                <div class="form-group">
                    <label for="taskDescription">任务描述</label>
                    <textarea id="taskDescription" placeholder="输入任务描述..."></textarea>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="taskPriority">优先级</label>
                        <select id="taskPriority">
                            <option value="low">低</option>
                            <option value="medium" selected>中</option>
                            <option value="high">高</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="taskCategory">分类</label>
                        <select id="taskCategory">
                            <option value="work">工作</option>
                            <option value="personal">个人</option>
                            <option value="study">学习</option>
                            <option value="health">健康</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="taskDueDate">截止日期</label>
                    <input type="datetime-local" id="taskDueDate">
                </div>
                <div class="modal-actions">
                    <button type="button" class="btn-cancel" onclick="closeModal()">取消</button>
                    <button type="submit" class="btn-save">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 确认删除模态框 -->
    <div class="modal" id="deleteModal">
        <div class="modal-content small">
            <div class="modal-header">
                <h2>确认删除</h2>
            </div>
            <p>确定要删除这个任务吗？此操作无法撤销。</p>
            <div class="modal-actions">
                <button type="button" class="btn-cancel" onclick="closeDeleteModal()">取消</button>
                <button type="button" class="btn-delete" onclick="confirmDelete()">删除</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
