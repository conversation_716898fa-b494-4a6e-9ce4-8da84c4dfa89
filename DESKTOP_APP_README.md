# Happi Reminder 桌面应用

一个基于Electron的快乐提醒桌面应用，采用现代化设计，支持系统托盘功能。

## 🌟 功能特色

### 🎨 现代化设计
- 基于你提供的1.css设计风格
- 鲜艳的色彩搭配和卡片式布局
- 流畅的动画效果和交互体验

### ⏰ 智能提醒系统
- **滑动时间选择**：不使用日期选择器，而是用滑动条选择时间
- **10天内提醒**：支持今天到10天后的提醒设置
- **系统通知**：到时间后弹出系统原生通知
- **托盘闪烁**：提醒时托盘图标会闪烁提示

### 🖥️ 桌面应用特性
- **系统托盘**：最小化到系统托盘，不占用任务栏
- **后台运行**：关闭窗口后继续在后台检查提醒
- **快捷操作**：托盘右键菜单快速添加提醒
- **跨平台**：支持Windows、macOS、Linux

## 🚀 安装和运行

### 前置要求
- Node.js (版本 16 或更高)
- npm 或 yarn

### 安装步骤

1. **安装依赖**
```bash
npm install
```

2. **运行开发版本**
```bash
npm start
```

3. **打包应用**
```bash
npm run build
```

### 图标文件
需要在 `assets` 文件夹中添加以下图标文件：
- `icon.png` - 应用主图标 (256x256)
- `tray-icon.png` - 托盘图标 (16x16 或 32x32)
- `tray-icon-alert.png` - 托盘提醒图标 (16x16 或 32x32)
- `icon.ico` - Windows图标
- `icon.icns` - macOS图标

## 🎯 使用方法

### 添加提醒
1. 在输入框中输入提醒内容
2. 使用滑动条选择时间：
   - **天数滑动条**：选择今天到10天后
   - **小时滑动条**：选择0-23小时
   - **分钟滑动条**：选择分钟（5分钟间隔）
3. 点击"添加提醒"按钮

### 系统托盘功能
- **双击托盘图标**：显示/隐藏主窗口
- **右键托盘图标**：显示菜单
  - 显示窗口
  - 添加提醒
  - 退出应用

### 提醒通知
- 到达提醒时间时会弹出系统通知
- 托盘图标会闪烁提示
- 点击通知可以聚焦到应用窗口

## 🎨 设计特色

### 颜色方案
- **主色调**：明亮黄色 (#fbbf24)
- **强调色**：纯黑色 (#000000)
- **背景色**：纯白色 (#ffffff)
- **卡片颜色**：6种鲜艳颜色循环

### 卡片设计
- 3D阴影效果
- 悬停动画
- 圆角边框
- 黑色边框线

### 时间滑动条
- 现代化滑动条设计
- 实时预览选中时间
- 直观的时间显示

## 📁 项目结构

```
happi-reminder/
├── main.js              # Electron主进程
├── renderer.js          # 渲染进程脚本
├── index.html           # 主页面
├── styles.css           # 样式文件
├── package.json         # 项目配置
├── assets/              # 图标资源
│   ├── icon.png
│   ├── tray-icon.png
│   └── tray-icon-alert.png
└── README.md
```

## 🔧 技术栈

- **Electron** - 桌面应用框架
- **HTML5/CSS3** - 界面和样式
- **JavaScript ES6+** - 应用逻辑
- **Node.js** - 后端支持

## 📝 开发说明

### 主要特性实现
1. **系统托盘**：使用Electron的Tray API
2. **系统通知**：使用Electron的Notification API
3. **IPC通信**：主进程和渲染进程间通信
4. **本地存储**：使用localStorage保存提醒数据
5. **定时检查**：setInterval定期检查提醒时间

### 自定义功能
- 滑动条时间选择器
- 托盘图标闪烁效果
- 卡片颜色循环系统
- 响应式布局设计

## 🎉 使用体验

这个应用完全按照你的要求设计：
- ✅ 模仿1.css的现代化设计风格
- ✅ 桌面应用with系统托盘
- ✅ 滑动条选择时间（不用日期选择器）
- ✅ 支持10天内的提醒设置
- ✅ 到时间右下角弹出通知

享受你的快乐提醒体验吧！🎯
